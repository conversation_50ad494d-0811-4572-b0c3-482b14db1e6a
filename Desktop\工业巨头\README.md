# 原油产业线配方配平计算器

## 文件夹结构
```
Desktop/工业巨头/
├── 配方表.xlsx                    # 配方数据表格文件
├── recipe_calculator.py           # 主要执行脚本 - 配方配平计算器
├── README.md                      # 项目说明文档
└── ~$配方表.xlsx                  # Excel临时文件（系统自动生成）
```

### 文件说明
- **配方表.xlsx**: 包含所有配方数据的Excel表格，每个sheet可包含多个配方，配方间用空行分隔
- **recipe_calculator.py**: 主执行脚本，负责读取配方表、解析数据并计算配平方案
- **README.md**: 项目文档，包含使用说明和配置信息

## 功能特点

1. **自动读取Excel配方表**: 支持多个工作表，自动识别配方间的空行分隔
2. **智能配方解析**: 自动解析配方中的物品名称和数量（负数表示需求，正数表示产出）
3. **副产物管理**: 可配置哪些副产物不需要配平为0
4. **配平计算**: 基于目标需求计算所需的配方倍数和总体平衡

## 使用方法

### 1. 安装依赖
```bash
pip install pandas numpy openpyxl
```

### 2. 配置副产物列表
在 `recipe_calculator.py` 中修改 `byproducts_to_keep` 列表：
```python
self.byproducts_to_keep = [
    '硫', '氨', '水', '氢气', '二氧化碳', 
    '高压蒸汽', '废气', '石脑油'
]
```

### 3. 运行脚本
```bash
python recipe_calculator.py
```

### 4. 自定义配平需求
在 `main()` 函数中修改 `target_items` 字典：
```python
target_items = {
    '物品名1': 需求量1,
    '物品名2': 需求量2,
    # 添加更多目标物品...
}
```

## 配方表格式要求

### Excel表格结构
- 每个工作表可包含多个配方
- 配方之间用空行分隔
- 每个配方的格式：
  - 第一行：配方名称（可选）
  - 后续行：物品名称和数量交替排列

### 数据格式示例
```
配方名称
物品A    -10    物品B    5
物品C    -5     物品D    3
（空行）
另一个配方
物品E    -8     物品F    12
```

## 输出说明

脚本会输出以下信息：
1. **加载的配方列表**: 显示所有从Excel中读取的配方
2. **配方倍数**: 每个配方需要运行的倍数
3. **净平衡**: 需要进一步配平的物品（目标是让这些为0）
4. **副产物**: 允许有剩余的副产物及其数量

## 注意事项

1. 确保Excel文件路径正确
2. 配方表中的数量格式应为数字
3. 负数表示消耗，正数表示产出
4. 可以随时修改副产物列表来调整配平策略

## 扩展功能

可以根据需要添加以下功能：
- 多目标优化算法
- 配方效率分析
- 图形化界面
- 配方依赖关系可视化
