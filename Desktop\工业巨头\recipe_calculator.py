#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原油产业线配方配平计算器
用于读取配方表Excel文件并计算配平方案
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class RecipeCalculator:
    def __init__(self, excel_path: str):
        """
        初始化配方计算器
        
        Args:
            excel_path: Excel配方表文件路径
        """
        self.excel_path = excel_path
        self.recipes = []
        
        # 不需要配平为0的副产物列表（可以随时修改）
        self.byproducts_to_keep = [
            '硫', '氨', '水', '氢气', '二氧化碳', 
            '高压蒸汽', '废气', '石脑油'
        ]
        
        self.load_recipes()
    
    def load_recipes(self):
        """从Excel文件加载配方数据"""
        try:
            # 读取Excel文件的所有sheet
            excel_file = pd.ExcelFile(self.excel_path)
            
            for sheet_name in excel_file.sheet_names:
                print(f"正在处理工作表: {sheet_name}")
                df = pd.read_excel(self.excel_path, sheet_name=sheet_name)
                
                # 解析该sheet中的配方
                sheet_recipes = self._parse_recipes_from_sheet(df, sheet_name)
                self.recipes.extend(sheet_recipes)
                
        except Exception as e:
            print(f"读取Excel文件时出错: {e}")
            return
    
    def _parse_recipes_from_sheet(self, df: pd.DataFrame, sheet_name: str) -> List[Dict]:
        """
        从单个sheet中解析配方数据
        配方之间用空行分隔
        """
        recipes = []
        current_recipe = []
        recipe_index = 0
        
        for index, row in df.iterrows():
            # 检查是否为空行（所有值都是NaN或空）
            if row.isna().all() or (row == '').all():
                # 如果当前配方不为空，保存它
                if current_recipe:
                    recipe_data = self._process_recipe_data(current_recipe, sheet_name, recipe_index)
                    if recipe_data:
                        recipes.append(recipe_data)
                        recipe_index += 1
                    current_recipe = []
            else:
                # 添加非空行到当前配方
                current_recipe.append(row)
        
        # 处理最后一个配方（如果文件末尾没有空行）
        if current_recipe:
            recipe_data = self._process_recipe_data(current_recipe, sheet_name, recipe_index)
            if recipe_data:
                recipes.append(recipe_data)
        
        return recipes
    
    def _process_recipe_data(self, recipe_rows: List, sheet_name: str, recipe_index: int) -> Optional[Dict]:
        """
        处理单个配方的数据
        假设第一行是配方名称，后续行是物品和数量
        """
        if not recipe_rows:
            return None
        
        try:
            # 创建DataFrame来处理配方数据
            recipe_df = pd.DataFrame(recipe_rows)
            
            # 假设第一行包含配方名称
            recipe_name = f"{sheet_name}_配方{recipe_index + 1}"
            if len(recipe_rows) > 0 and not pd.isna(recipe_rows[0].iloc[0]):
                recipe_name = str(recipe_rows[0].iloc[0])
            
            # 解析物品和数量
            items = {}
            for i, row in enumerate(recipe_rows):
                if i == 0:  # 跳过标题行
                    continue
                
                # 假设格式为：物品名称 | 数量
                for col_idx in range(len(row)):
                    if col_idx % 2 == 0:  # 物品名称列
                        item_name = row.iloc[col_idx]
                        if pd.notna(item_name) and item_name != '':
                            # 获取对应的数量
                            if col_idx + 1 < len(row):
                                quantity = row.iloc[col_idx + 1]
                                if pd.notna(quantity) and str(quantity).replace('-', '').replace('.', '').isdigit():
                                    items[str(item_name)] = float(quantity)
            
            if items:
                return {
                    'name': recipe_name,
                    'sheet': sheet_name,
                    'items': items
                }
        
        except Exception as e:
            print(f"处理配方数据时出错: {e}")
        
        return None
    
    def calculate_basic_balance(self) -> Dict:
        """
        计算基本配平方案 - 找到让所有非副产物平衡的整数倍数

        Returns:
            配平结果字典
        """
        if not self.recipes:
            return {'error': '没有加载到配方数据'}

        print("开始计算基本配平方案...")

        # 收集所有物品
        all_items = set()
        for recipe in self.recipes:
            all_items.update(recipe['items'].keys())

        # 分离需要配平的物品和副产物
        items_to_balance = [item for item in all_items if item not in self.byproducts_to_keep]

        print(f"需要配平的物品: {items_to_balance}")
        print(f"副产物（允许有剩余）: {self.byproducts_to_keep}")

        # 使用简单的整数倍数搜索
        best_result = self._find_integer_multipliers(items_to_balance)

        return best_result

    def _find_integer_multipliers(self, items_to_balance: List[str], max_multiplier: int = 10) -> Dict:
        """
        寻找整数倍数使物品平衡
        使用分层搜索策略提高效率

        Args:
            items_to_balance: 需要平衡的物品列表
            max_multiplier: 每个配方的最大倍数

        Returns:
            最佳配平结果
        """
        print(f"正在搜索最佳整数倍数组合（每个配方0-{max_multiplier}倍）...")

        # 先尝试小倍数范围
        for max_mult in [3, 5, 8, max_multiplier]:
            print(f"尝试倍数范围 0-{max_mult}...")
            result = self._search_multipliers_in_range(items_to_balance, max_mult)
            if result and 'error' not in result:
                # 检查是否找到完美平衡
                imbalance = sum(abs(balance) for balance in result['net_balance'].values())
                if imbalance < 0.001:
                    print(f"在倍数范围 0-{max_mult} 中找到完美平衡!")
                    return result
                elif imbalance < 1.0:  # 接近平衡
                    print(f"在倍数范围 0-{max_mult} 中找到较好平衡，不平衡度: {imbalance:.3f}")
                    return result

        return {'error': '未找到可行方案'}

    def _search_multipliers_in_range(self, items_to_balance: List[str], max_mult: int) -> Dict:
        """在指定范围内搜索倍数"""
        from itertools import product

        best_result = None
        best_imbalance = float('inf')

        # 生成所有可能的整数倍数组合
        multiplier_ranges = [range(0, max_mult + 1) for _ in self.recipes]

        tested_combinations = 0
        for multipliers in product(*multiplier_ranges):
            # 跳过全零组合
            if all(m == 0 for m in multipliers):
                continue

            tested_combinations += 1

            # 计算这个倍数组合的结果
            result = self._calculate_result_for_multipliers(multipliers, items_to_balance)

            # 计算不平衡程度
            imbalance = sum(abs(balance) for balance in result['net_balance'].values())

            # 如果找到完美平衡，立即返回
            if imbalance < 0.001:
                print(f"找到完美平衡! 测试了 {tested_combinations} 种组合")
                return result

            # 记录最佳结果
            if imbalance < best_imbalance:
                best_imbalance = imbalance
                best_result = result

        print(f"在范围 0-{max_mult} 中测试了 {tested_combinations} 种组合，最佳不平衡度: {best_imbalance:.3f}")

        return best_result if best_result else None

    def _calculate_result_for_multipliers(self, multipliers: Tuple, items_to_balance: List[str]) -> Dict:
        """
        计算给定倍数组合的结果
        """
        result = {
            'recipe_multipliers': {},
            'total_production': {},
            'total_consumption': {},
            'net_balance': {},
            'byproducts': {}
        }

        # 应用每个配方的倍数
        for recipe, multiplier in zip(self.recipes, multipliers):
            if multiplier > 0:
                result['recipe_multipliers'][recipe['name']] = multiplier

                # 计算该配方的贡献
                for item, amount in recipe['items'].items():
                    total_amount = amount * multiplier

                    if amount > 0:  # 产出
                        result['total_production'][item] = result['total_production'].get(item, 0) + total_amount
                    else:  # 消耗
                        result['total_consumption'][item] = result['total_consumption'].get(item, 0) + abs(total_amount)

        # 计算净平衡
        all_items = set(result['total_production'].keys()) | set(result['total_consumption'].keys())
        for item in all_items:
            production = result['total_production'].get(item, 0)
            consumption = result['total_consumption'].get(item, 0)
            net = production - consumption

            if item in items_to_balance:
                result['net_balance'][item] = net
            elif item in self.byproducts_to_keep:
                result['byproducts'][item] = net

        return result
    
    def print_recipes(self):
        """打印所有加载的配方"""
        print(f"共加载了 {len(self.recipes)} 个配方:")
        for i, recipe in enumerate(self.recipes):
            print(f"\n配方 {i+1}: {recipe['name']} (来自: {recipe['sheet']})")
            for item, amount in recipe['items'].items():
                sign = "+" if amount > 0 else ""
                print(f"  {item}: {sign}{amount}")
    
    def print_balance_result(self, result: Dict):
        """打印配平结果"""
        if 'error' in result:
            print(f"错误: {result['error']}")
            return
        
        print("\n=== 配方配平结果 ===")
        
        print("\n使用的配方及倍数:")
        for recipe_name, multiplier in result['recipe_multipliers'].items():
            print(f"  {recipe_name}: {multiplier:.3f}倍")
        
        print("\n净平衡 (需要进一步配平的物品):")
        if result['net_balance']:
            for item, balance in result['net_balance'].items():
                status = "过剩" if balance > 0 else "不足"
                print(f"  {item}: {balance:.3f} ({status})")
        else:
            print("  所有物品已配平!")
        
        print("\n副产物:")
        for item, amount in result['byproducts'].items():
            sign = "+" if amount > 0 else ""
            print(f"  {item}: {sign}{amount:.3f}")


def main():
    """主函数"""
    # 配方表文件路径
    excel_path = "配方表.xlsx"

    # 创建计算器实例
    calculator = RecipeCalculator(excel_path)

    # 打印加载的配方
    calculator.print_recipes()

    # 计算基本配平方案
    print("\n" + "="*50)
    print("计算基本配平方案（整数倍数）")

    result = calculator.calculate_basic_balance()
    calculator.print_balance_result(result)


if __name__ == "__main__":
    main()
