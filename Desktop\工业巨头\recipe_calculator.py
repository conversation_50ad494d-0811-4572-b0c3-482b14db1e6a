#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原油产业线配方配平计算器
用于读取配方表Excel文件并计算配平方案
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class RecipeCalculator:
    def __init__(self, excel_path: str):
        """
        初始化配方计算器
        
        Args:
            excel_path: Excel配方表文件路径
        """
        self.excel_path = excel_path
        self.recipes = []
        
        # 不需要配平为0的副产物列表（可以随时修改）
        self.byproducts_to_keep = [
            '硫', '氨', '水', '氢气', '二氧化碳', 
            '高压蒸汽', '废气', '石脑油'
        ]
        
        self.load_recipes()
    
    def load_recipes(self):
        """从Excel文件加载配方数据"""
        try:
            # 读取Excel文件的所有sheet
            excel_file = pd.ExcelFile(self.excel_path)
            
            for sheet_name in excel_file.sheet_names:
                print(f"正在处理工作表: {sheet_name}")
                df = pd.read_excel(self.excel_path, sheet_name=sheet_name)
                
                # 解析该sheet中的配方
                sheet_recipes = self._parse_recipes_from_sheet(df, sheet_name)
                self.recipes.extend(sheet_recipes)
                
        except Exception as e:
            print(f"读取Excel文件时出错: {e}")
            return
    
    def _parse_recipes_from_sheet(self, df: pd.DataFrame, sheet_name: str) -> List[Dict]:
        """
        从单个sheet中解析配方数据
        配方之间用空行分隔
        """
        recipes = []
        current_recipe = []
        recipe_index = 0
        
        for index, row in df.iterrows():
            # 检查是否为空行（所有值都是NaN或空）
            if row.isna().all() or (row == '').all():
                # 如果当前配方不为空，保存它
                if current_recipe:
                    recipe_data = self._process_recipe_data(current_recipe, sheet_name, recipe_index)
                    if recipe_data:
                        recipes.append(recipe_data)
                        recipe_index += 1
                    current_recipe = []
            else:
                # 添加非空行到当前配方
                current_recipe.append(row)
        
        # 处理最后一个配方（如果文件末尾没有空行）
        if current_recipe:
            recipe_data = self._process_recipe_data(current_recipe, sheet_name, recipe_index)
            if recipe_data:
                recipes.append(recipe_data)
        
        return recipes
    
    def _process_recipe_data(self, recipe_rows: List, sheet_name: str, recipe_index: int) -> Optional[Dict]:
        """
        处理单个配方的数据
        假设第一行是配方名称，后续行是物品和数量
        """
        if not recipe_rows:
            return None
        
        try:
            # 创建DataFrame来处理配方数据
            recipe_df = pd.DataFrame(recipe_rows)
            
            # 假设第一行包含配方名称
            recipe_name = f"{sheet_name}_配方{recipe_index + 1}"
            if len(recipe_rows) > 0 and not pd.isna(recipe_rows[0].iloc[0]):
                recipe_name = str(recipe_rows[0].iloc[0])
            
            # 解析物品和数量
            items = {}
            for i, row in enumerate(recipe_rows):
                if i == 0:  # 跳过标题行
                    continue
                
                # 假设格式为：物品名称 | 数量
                for col_idx in range(len(row)):
                    if col_idx % 2 == 0:  # 物品名称列
                        item_name = row.iloc[col_idx]
                        if pd.notna(item_name) and item_name != '':
                            # 获取对应的数量
                            if col_idx + 1 < len(row):
                                quantity = row.iloc[col_idx + 1]
                                if pd.notna(quantity) and str(quantity).replace('-', '').replace('.', '').isdigit():
                                    items[str(item_name)] = float(quantity)
            
            if items:
                return {
                    'name': recipe_name,
                    'sheet': sheet_name,
                    'items': items
                }
        
        except Exception as e:
            print(f"处理配方数据时出错: {e}")
        
        return None
    
    def balance_recipes(self, target_items: Dict[str, float], max_iterations: int = 1000) -> Dict:
        """
        计算配方配平方案
        
        Args:
            target_items: 目标物品及其需求量 {'物品名': 需求量}
            max_iterations: 最大迭代次数
            
        Returns:
            配平结果字典
        """
        if not self.recipes:
            return {'error': '没有加载到配方数据'}
        
        # 初始化结果
        result = {
            'recipe_multipliers': {},  # 每个配方的倍数
            'total_production': {},    # 总产出
            'total_consumption': {},   # 总消耗
            'net_balance': {},         # 净平衡
            'byproducts': {}          # 副产物
        }
        
        # 简单的贪心算法进行配平
        for target_item, target_amount in target_items.items():
            print(f"正在配平: {target_item} (需求: {target_amount})")
            
            # 找到能生产该物品的配方
            producing_recipes = []
            for recipe in self.recipes:
                if target_item in recipe['items'] and recipe['items'][target_item] > 0:
                    producing_recipes.append(recipe)
            
            if not producing_recipes:
                print(f"警告: 没有找到生产 {target_item} 的配方")
                continue
            
            # 选择第一个配方（可以优化为选择最优配方）
            selected_recipe = producing_recipes[0]
            production_per_cycle = selected_recipe['items'][target_item]
            
            # 计算需要的配方倍数
            multiplier = target_amount / production_per_cycle
            result['recipe_multipliers'][selected_recipe['name']] = multiplier
            
            # 计算该配方的总影响
            for item, amount in selected_recipe['items'].items():
                total_amount = amount * multiplier
                
                if amount > 0:  # 产出
                    result['total_production'][item] = result['total_production'].get(item, 0) + total_amount
                else:  # 消耗
                    result['total_consumption'][item] = result['total_consumption'].get(item, 0) + abs(total_amount)
        
        # 计算净平衡
        all_items = set(result['total_production'].keys()) | set(result['total_consumption'].keys())
        for item in all_items:
            production = result['total_production'].get(item, 0)
            consumption = result['total_consumption'].get(item, 0)
            net = production - consumption
            
            # 根据副产物列表决定是否需要配平为0
            if item not in self.byproducts_to_keep and abs(net) > 0.001:
                result['net_balance'][item] = net
            elif item in self.byproducts_to_keep:
                result['byproducts'][item] = net
        
        return result
    
    def print_recipes(self):
        """打印所有加载的配方"""
        print(f"共加载了 {len(self.recipes)} 个配方:")
        for i, recipe in enumerate(self.recipes):
            print(f"\n配方 {i+1}: {recipe['name']} (来自: {recipe['sheet']})")
            for item, amount in recipe['items'].items():
                sign = "+" if amount > 0 else ""
                print(f"  {item}: {sign}{amount}")
    
    def print_balance_result(self, result: Dict):
        """打印配平结果"""
        if 'error' in result:
            print(f"错误: {result['error']}")
            return
        
        print("\n=== 配方配平结果 ===")
        
        print("\n使用的配方及倍数:")
        for recipe_name, multiplier in result['recipe_multipliers'].items():
            print(f"  {recipe_name}: {multiplier:.3f}倍")
        
        print("\n净平衡 (需要进一步配平的物品):")
        if result['net_balance']:
            for item, balance in result['net_balance'].items():
                status = "过剩" if balance > 0 else "不足"
                print(f"  {item}: {balance:.3f} ({status})")
        else:
            print("  所有物品已配平!")
        
        print("\n副产物:")
        for item, amount in result['byproducts'].items():
            sign = "+" if amount > 0 else ""
            print(f"  {item}: {sign}{amount:.3f}")


def main():
    """主函数"""
    # 配方表文件路径
    excel_path = "配方表.xlsx"
    
    # 创建计算器实例
    calculator = RecipeCalculator(excel_path)
    
    # 打印加载的配方
    calculator.print_recipes()
    
    # 示例：配平计算
    print("\n" + "="*50)
    print("示例配平计算")
    
    # 定义目标需求
    target_items = {
        # '物品名': 需求量
        # 请根据实际需求修改
    }
    
    if target_items:
        result = calculator.balance_recipes(target_items)
        calculator.print_balance_result(result)
    else:
        print("请在 target_items 中定义目标需求")


if __name__ == "__main__":
    main()
