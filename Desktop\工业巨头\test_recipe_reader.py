#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配方读取测试脚本
用于测试和调试配方表读取功能
"""

import pandas as pd
import sys
import os

def test_excel_reading():
    """测试Excel文件读取"""
    excel_path = "配方表.xlsx"
    
    if not os.path.exists(excel_path):
        print(f"错误: 找不到文件 {excel_path}")
        return False
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(excel_path)
        print(f"Excel文件包含的工作表: {excel_file.sheet_names}")
        
        # 读取每个工作表的前几行来了解数据结构
        for sheet_name in excel_file.sheet_names:
            print(f"\n=== 工作表: {sheet_name} ===")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            print(f"数据形状: {df.shape}")
            print("前10行数据:")
            print(df.head(10))
            
            # 检查空行
            empty_rows = df.index[df.isna().all(axis=1)].tolist()
            print(f"空行位置: {empty_rows}")
            
        return True
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return False

def analyze_data_pattern():
    """分析数据模式"""
    excel_path = "配方表.xlsx"
    
    try:
        excel_file = pd.ExcelFile(excel_path)
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n=== 分析工作表: {sheet_name} ===")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            
            # 分析每一行的数据类型和模式
            for index, row in df.iterrows():
                if not row.isna().all():  # 非空行
                    non_null_values = row.dropna().tolist()
                    print(f"行 {index + 1}: {non_null_values}")
                else:
                    print(f"行 {index + 1}: [空行]")
                
                if index >= 20:  # 只显示前20行
                    print("... (更多数据)")
                    break
                    
    except Exception as e:
        print(f"分析数据时出错: {e}")

def main():
    """主测试函数"""
    print("配方表读取测试")
    print("=" * 50)
    
    # 测试Excel读取
    if test_excel_reading():
        print("\n" + "=" * 50)
        print("数据模式分析")
        analyze_data_pattern()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
